{"env": {"browser": true, "es6": true}, "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2019, "sourceType": "module"}, "globals": {"Atomics": "readonly", "SharedArrayBuffer": "readonly"}, "plugins": ["import", "jsx-a11y", "react", "prettier"], "extends": ["airbnb", "prettier", "next", "next/core-web-vitals"], "rules": {"prettier/prettier": ["error", {"endOfLine": "auto"}], "camelcase": "off", "import/prefer-default-export": "off", "react/prop-types": "off", "react/jsx-filename-extension": "off", "react/jsx-props-no-spreading": "off", "react/no-unused-prop-types": "off", "react/react-in-jsx-scope": "off", "react/require-default-props": "off", "import/extensions": ["error", "ignorePackages", {"ts": "never", "tsx": "never", "js": "never", "jsx": "never"}], "quotes": "off", "jsx-a11y/anchor-is-valid": ["error", {"components": ["Link"], "specialLink": ["hrefLeft", "hrefRight"], "aspects": ["<PERSON><PERSON><PERSON><PERSON>", "preferButton"]}]}, "overrides": [{"files": "**/*.+(ts|tsx)", "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint/eslint-plugin"], "extends": ["plugin:@typescript-eslint/recommended", "prettier"], "rules": {"@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "no-use-before-define": [0], "@typescript-eslint/no-use-before-define": [1], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-var-requires": "off"}}], "settings": {"import/resolver": {"typescript": {"project": "."}}, "react": {"version": "detect"}}}