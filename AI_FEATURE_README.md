# AI Avatar Analysis Feature

This document describes the new AI-powered avatar analysis feature added to the Notion Avatar Maker.

## Overview

The AI Analysis feature allows users to upload a photo of themselves and receive intelligent suggestions for avatar components that match their appearance. The AI analyzes facial features, hair style, accessories, and other characteristics to automatically configure the avatar.

## How It Works

1. **Upload Photo**: Users click the "🤖 AI Analysis" button and upload a photo
2. **AI Analysis**: OpenAI's GPT-4 Vision API analyzes the photo
3. **Component Mapping**: AI suggests values for each avatar component:
   - Face shape (0-15)
   - Hair style (0-57)
   - Eyes (0-13)
   - Eyebrows (0-15)
   - Nose (0-13)
   - Mouth (0-19)
   - Glass<PERSON> (0-13)
   - Beard (0-15)
   - Accessories (0-13)
   - Details (0-12)
4. **Auto-Configuration**: Avatar updates automatically with AI suggestions
5. **Customization**: Users can further customize the avatar as normal

## Technical Details

### Files Added/Modified

- `src/pages/api/analyze-photo.ts` - AI analysis API endpoint
- `src/pages/components/Modal/PhotoUpload/index.tsx` - Photo upload modal
- `src/pages/components/AvatarEditor/index.tsx` - Integration with main editor
- `src/const.ts` - Added photoUpload to ModalKeyMap
- `.env.local` - OpenAI API key configuration

### API Integration

The feature uses OpenAI's GPT-4 Vision API to analyze uploaded photos. The AI is prompted to return a JSON object with numerical values for each avatar component based on the person's appearance.

### Error Handling

- File type validation (images only)
- File size limits (5MB max)
- API error handling
- Graceful fallbacks

## Setup

1. Add your OpenAI API key to `.env.local`:
   ```
   OPENAI_API_KEY=your-api-key-here
   ```

2. Install dependencies:
   ```bash
   npm install openai
   ```

3. Build and run:
   ```bash
   npm run build
   npm run dev
   ```

## Usage

1. Navigate to the avatar editor
2. Click the "🤖 AI Analysis" button (purple button next to Random)
3. Upload a clear photo of yourself
4. Wait for AI analysis (usually 3-5 seconds)
5. Avatar will update automatically with AI suggestions
6. Customize further as desired

## Limitations

- Requires OpenAI API key
- Works best with clear, front-facing photos
- AI suggestions are approximate and may need manual adjustment
- Limited to supported image formats (JPG, PNG)
- 5MB file size limit