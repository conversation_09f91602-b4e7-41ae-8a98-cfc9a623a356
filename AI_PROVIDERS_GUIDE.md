# AI Avatar Analysis - Multiple Provider Support

The Notion Avatar AI now supports multiple AI providers for photo analysis! You can choose from OpenAI, Google Gemini, OpenRouter, or Anthropic Claude based on your preferences, budget, and requirements.

## Available AI Providers

### 1. OpenAI GPT-4 Vision (Default)
- **Model**: `gpt-4-vision-preview`
- **Pros**: High accuracy, excellent instruction following
- **Cons**: Most expensive option
- **Setup**: Requires OpenAI API key

### 2. Google Gemini Pro Vision
- **Model**: `gemini-pro-vision`
- **Pros**: Good performance, competitive pricing, Google ecosystem
- **Cons**: May have regional availability restrictions
- **Setup**: Requires Google AI Studio API key

### 3. OpenRouter (Recommended for flexibility)
- **Models**: Access to multiple providers through one API
  - `openai/gpt-4-vision-preview` (OpenAI)
  - `google/gemini-pro-vision` (Google)
  - `anthropic/claude-3-haiku` (Fast, cheapest Claude)
  - `anthropic/claude-3-sonnet` (Balanced Claude)
  - `anthropic/claude-3-opus` (Most capable Claude)
- **Pros**: Multiple models, competitive pricing, single API integration
- **Cons**: Additional abstraction layer
- **Setup**: Requires OpenRouter API key

### 4. Anthropic Claude 3 (Direct API)
- **Models**: 
  - `claude-3-haiku-20240307` (Fastest, cheapest)
  - `claude-3-sonnet-********` (Balanced, default)
  - `claude-3-opus-********` (Most capable)
- **Pros**: Strong vision capabilities, safety-focused
- **Cons**: Limited availability in some regions
- **Setup**: Requires Anthropic API key

## Configuration

### Environment Variables

Add these to your `.env.local` file:

```bash
# Choose your provider (openai, gemini, openrouter, claude)
AI_PROVIDER=openai

# OpenAI (default)
OPENAI_API_KEY=your-openai-api-key-here

# Google Gemini
GEMINI_API_KEY=your-gemini-api-key-here

# OpenRouter (access to multiple models)
OPENROUTER_API_KEY=your-openrouter-api-key-here
OPENROUTER_MODEL=openai/gpt-4-vision-preview
SITE_URL=https://your-domain.com

# Anthropic Claude (direct)
ANTHROPIC_API_KEY=your-anthropic-api-key-here
CLAUDE_MODEL=claude-3-sonnet-********
```

### Provider Selection

Set the `AI_PROVIDER` environment variable to choose your provider:

- `AI_PROVIDER=openai` - Uses OpenAI GPT-4 Vision (default)
- `AI_PROVIDER=gemini` - Uses Google Gemini Pro Vision
- `AI_PROVIDER=openrouter` - Uses OpenRouter (specify model with `OPENROUTER_MODEL`)
- `AI_PROVIDER=claude` - Uses Anthropic Claude 3 (specify model with `CLAUDE_MODEL`)

## Getting API Keys

### OpenAI
1. Visit [OpenAI API](https://platform.openai.com/api-keys)
2. Create an account and add billing information
3. Generate an API key

### Google Gemini
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key to your environment

### OpenRouter
1. Visit [OpenRouter](https://openrouter.ai/)
2. Sign up and go to [Keys](https://openrouter.ai/keys)
3. Create an API key
4. Set your site URL for proper attribution

### Anthropic Claude
1. Visit [Anthropic Console](https://console.anthropic.com/)
2. Sign up and go to API Keys
3. Create an API key

## Pricing Comparison (Approximate)

| Provider | Model | Input (per 1K tokens) | Notes |
|----------|--------|---------------------|-------|
| OpenAI | GPT-4 Vision | $0.01 | Most expensive but reliable |
| Gemini | Gemini Pro Vision | $0.00025 | Very cost-effective |
| OpenRouter | Various | Varies | Compare models on their site |
| Claude | Claude 3 Haiku | $0.00025 | Cheapest Claude option |
| Claude | Claude 3 Sonnet | $0.003 | Balanced option |
| Claude | Claude 3 Opus | $0.015 | Most capable |

*Prices are subject to change. Check provider websites for current pricing.*

## Switching Providers

To switch providers, simply:

1. Update your `.env.local` file with the new provider and API key
2. Restart your development server
3. The AI Analysis button will automatically use the new provider

Example switching from OpenAI to Gemini:

```bash
# Change from:
AI_PROVIDER=openai
OPENAI_API_KEY=your-openai-key

# To:
AI_PROVIDER=gemini  
GEMINI_API_KEY=your-gemini-key
```

## Recommendations

### For Development/Testing
- **Google Gemini**: Very cost-effective, good performance
- **OpenRouter + Claude Haiku**: Fast and cheap

### For Production
- **OpenAI GPT-4 Vision**: Most reliable and accurate
- **OpenRouter + Claude Sonnet**: Good balance of cost and performance

### For High Volume
- **Google Gemini**: Most cost-effective at scale
- **OpenRouter**: Flexibility to switch models based on usage

## Troubleshooting

### Common Issues

1. **"API key not configured"**
   - Make sure you've set the correct environment variable for your chosen provider
   - Restart your development server after adding keys

2. **"Provider not found"**
   - Check that `AI_PROVIDER` is set to a valid option: `openai`, `gemini`, `openrouter`, or `claude`

3. **OpenRouter specific issues**
   - Make sure `SITE_URL` is set for proper attribution
   - Verify the model name is correct (check OpenRouter's model list)

4. **Regional restrictions**
   - Some providers have geographic limitations
   - Consider using OpenRouter as it may provide access in more regions

### Testing Your Configuration

The API will log which provider it's using when processing requests. Check your console for:
```
Using AI provider: [Provider Name]
```

## Security Notes

- Never commit API keys to version control
- Use environment variables for all sensitive configuration
- Rotate API keys regularly
- Monitor usage to prevent unexpected costs
- Consider rate limiting for production applications

## Feature Comparison

| Feature | OpenAI | Gemini | OpenRouter | Claude |
|---------|--------|--------|------------|--------|
| Vision Analysis | ✅ Excellent | ✅ Very Good | ✅ Varies by model | ✅ Very Good |
| JSON Following | ✅ Excellent | ✅ Good | ✅ Varies | ✅ Excellent |
| Speed | ⚡ Fast | ⚡ Very Fast | ⚡ Varies | ⚡ Fast |
| Cost | 💰 High | 💰 Low | 💰 Varies | 💰 Medium |
| Availability | 🌍 Global | 🌍 Limited | 🌍 Good | 🌍 Limited |

Choose the provider that best fits your needs!