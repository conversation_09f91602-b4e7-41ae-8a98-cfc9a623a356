NEXT_PUBLIC_GOOGLE_ANALYTICS=<Your_tracking_ID>
NEXT_PUBLIC_URL=http://localhost:3000

# AI Provider Configuration
# Choose one provider: openai, gemini, openrouter, claude
AI_PROVIDER=openai

# OpenAI Configuration (default provider)
OPENAI_API_KEY=your-openai-api-key-here

# Google Gemini Configuration
GEMINI_API_KEY=your-gemini-api-key-here

# OpenRouter Configuration (supports multiple models)
OPENROUTER_API_KEY=your-openrouter-api-key-here
OPENROUTER_MODEL=openai/gpt-4-vision-preview
# Other OpenRouter models you can use:
# OPENROUTER_MODEL=google/gemini-pro-vision
# OPENROUTER_MODEL=anthropic/claude-3-haiku
# OPENROUTER_MODEL=anthropic/claude-3-sonnet
# OPENROUTER_MODEL=anthropic/claude-3-opus

# Anthropic Claude Configuration (direct API)
ANTHROPIC_API_KEY=your-anthropic-api-key-here
CLAUDE_MODEL=claude-3-sonnet-20240229
# Other Claude models:
# CLAUDE_MODEL=claude-3-haiku-20240307
# CLAUDE_MODEL=claude-3-opus-20240229

# Site URL for OpenRouter referrer header
SITE_URL=http://localhost:3000