import {
  <PERSON><PERSON><PERSON>ider,
  AvatarConfig,
  ANALYSIS_PROMPT,
  parseAIResponse,
} from './ai-providers';

// OpenAI Provider
export class OpenA<PERSON><PERSON>ider implements AIProvider {
  name = 'OpenAI GPT-4 Vision';

  private openai: any;

  constructor() {
    if (process.env.OPENAI_API_KEY) {
      // eslint-disable-next-line global-require
      const { OpenAI } = require('openai');
      this.openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
    }
  }

  async analyzePhoto(imageBase64: string): Promise<AvatarConfig> {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OpenAI API key not configured');
    }

    const response = await this.openai.chat.completions.create({
      model: 'gpt-4-vision-preview',
      messages: [
        {
          role: 'user',
          content: [
            { type: 'text', text: ANALYSIS_PROMPT },
            {
              type: 'image_url',
              image_url: {
                url: `data:image/jpeg;base64,${imageBase64}`,
                detail: 'high',
              },
            },
          ],
        },
      ],
      max_tokens: 300,
    });

    return parseAIResponse(response.choices[0]?.message?.content, this.name);
  }
}
