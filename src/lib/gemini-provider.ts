import {
  <PERSON>Provider,
  AvatarConfig,
  ANALYSIS_PROMPT,
  parseAIResponse,
} from './ai-providers';

// Google Gemini Provider
export class <PERSON><PERSON><PERSON>ider implements AIProvider {
  name = 'Google Gemini Pro Vision';

  async analyzePhoto(imageBase64: string): Promise<AvatarConfig> {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('Gemini API key not configured');
    }

    // eslint-disable-next-line global-require
    const { GoogleGenerativeAI } = require('@google/generative-ai');
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: 'gemini-pro-vision' });

    const result = await model.generateContent([
      ANALYSIS_PROMPT,
      {
        inlineData: {
          data: imageBase64,
          mimeType: 'image/jpeg',
        },
      },
    ]);

    const response = await result.response;
    const content = response.text();

    return parseAIResponse(content, this.name);
  }
}
