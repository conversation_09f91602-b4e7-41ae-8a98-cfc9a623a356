import {
  <PERSON>Provider,
  AvatarConfig,
  ANALYSIS_PROMPT,
  parseAIResponse,
} from './ai-providers';

// Anthropic Claude Provider (via direct API)
export class Claude<PERSON>rovider implements AIProvider {
  name = 'Anthropic Claude 3';

  private model: string;

  constructor(model = 'claude-3-sonnet-20240229') {
    this.model = model;
  }

  async analyzePhoto(imageBase64: string): Promise<AvatarConfig> {
    if (!process.env.ANTHROPIC_API_KEY) {
      throw new Error('Anthropic API key not configured');
    }

    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': process.env.ANTHROPIC_API_KEY,
        'anthropic-version': '2023-06-01',
        'content-type': 'application/json',
      },
      body: JSON.stringify({
        model: this.model,
        max_tokens: 300,
        messages: [
          {
            role: 'user',
            content: [
              { type: 'text', text: ANALYSIS_PROMPT },
              {
                type: 'image',
                source: {
                  type: 'base64',
                  media_type: 'image/jpeg',
                  data: imageBase64,
                },
              },
            ],
          },
        ],
      }),
    });

    if (!response.ok) {
      throw new Error(`Claude API error: ${response.statusText}`);
    }

    const result = await response.json();
    return parseAIResponse(result.content[0]?.text, this.name);
  }
}
