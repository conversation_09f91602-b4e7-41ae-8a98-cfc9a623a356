// AI Provider abstraction layer for photo analysis
export interface AvatarConfig {
  face: number;
  hair: number;
  eyes: number;
  eyebrows: number;
  nose: number;
  mouth: number;
  glasses: number;
  beard: number;
  accessories: number;
  details: number;
}

export interface AIProvider {
  name: string;
  analyzePhoto(imageBase64: string): Promise<AvatarConfig>;
}

export const ANALYSIS_PROMPT = `Analyze this photo and suggest avatar components that would best match this person's appearance. Return ONLY a JSON object with the following structure:
{
  "face": number (0-15, face shape - rounder faces use lower numbers, more angular use higher),
  "hair": number (0-57, hair style and length - consider color, length, and style),
  "eyes": number (0-13, eye shape and size),
  "eyebrows": number (0-15, eyebrow thickness and shape),
  "nose": number (0-13, nose size and shape),
  "mouth": number (0-19, mouth size and expression),
  "glasses": number (0-13, 0 for no glasses, 1-13 for different styles if person wears glasses),
  "beard": number (0-15, 0 for no beard, 1-15 for different beard styles if person has facial hair),
  "accessories": number (0-13, usually 0 unless person has distinctive accessories),
  "details": number (0-12, usually 0 unless person has distinctive facial features like scars or marks)
}

Consider the person's apparent gender, age, facial structure, hair style and color, facial hair, glasses, and overall appearance. Choose numbers that would create an avatar most similar to this person.`;

// Shared validation function
export function validateAvatarConfig(config: any): AvatarConfig {
  const requiredFields = [
    'face',
    'hair',
    'eyes',
    'eyebrows',
    'nose',
    'mouth',
    'glasses',
    'beard',
    'accessories',
    'details',
  ];

  requiredFields.forEach((field) => {
    if (typeof config[field] !== 'number') {
      throw new Error(`Invalid or missing field: ${field}`);
    }
  });

  return config as AvatarConfig;
}

// Shared response parsing function
export function parseAIResponse(
  content: string | null,
  providerName: string,
): AvatarConfig {
  if (!content) {
    throw new Error(`No response from ${providerName}`);
  }

  let avatarConfig;
  try {
    avatarConfig = JSON.parse(content);
  } catch (parseError) {
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      avatarConfig = JSON.parse(jsonMatch[0]);
    } else {
      throw new Error(
        `Could not parse avatar configuration from ${providerName} response`,
      );
    }
  }

  return validateAvatarConfig(avatarConfig);
}

// Provider factory
export function createAIProvider(): AIProvider {
  const provider = process.env.AI_PROVIDER || 'openai';

  switch (provider.toLowerCase()) {
    case 'gemini':
    case 'google': {
      // eslint-disable-next-line global-require
      const { GeminiProvider } = require('./gemini-provider');
      return new GeminiProvider();
    }

    case 'openrouter': {
      const model =
        process.env.OPENROUTER_MODEL || 'openai/gpt-4-vision-preview';
      // eslint-disable-next-line global-require
      const { OpenRouterProvider } = require('./openrouter-provider');
      return new OpenRouterProvider(model);
    }

    case 'claude':
    case 'anthropic': {
      const claudeModel =
        process.env.CLAUDE_MODEL || 'claude-3-sonnet-20240229';
      // eslint-disable-next-line global-require
      const { ClaudeProvider } = require('./claude-provider');
      return new ClaudeProvider(claudeModel);
    }

    case 'openai':
    default: {
      // eslint-disable-next-line global-require
      const { OpenAIProvider } = require('./openai-provider');
      return new OpenAIProvider();
    }
  }
}
