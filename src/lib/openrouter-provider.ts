import {
  <PERSON>Provider,
  AvatarConfig,
  ANALYSIS_PROMPT,
  parseAIResponse,
} from './ai-providers';

// OpenRouter Provider (supports multiple models)
export class OpenRouterProvider implements AIProvider {
  name = 'OpenRouter';

  private model: string;

  constructor(model = 'openai/gpt-4-vision-preview') {
    this.model = model;
  }

  async analyzePhoto(imageBase64: string): Promise<AvatarConfig> {
    if (!process.env.OPENROUTER_API_KEY) {
      throw new Error('OpenRouter API key not configured');
    }

    const response = await fetch(
      'https://openrouter.ai/api/v1/chat/completions',
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${process.env.OPENROUTER_API_KEY}`,
          'HTTP-Referer': process.env.SITE_URL || 'http://localhost:3000',
          'X-Title': 'Notion Avatar AI',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            {
              role: 'user',
              content: [
                { type: 'text', text: ANALYSIS_PROMPT },
                {
                  type: 'image_url',
                  image_url: {
                    url: `data:image/jpeg;base64,${imageBase64}`,
                  },
                },
              ],
            },
          ],
          max_tokens: 300,
        }),
      },
    );

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.statusText}`);
    }

    const result = await response.json();
    return parseAIResponse(result.choices[0]?.message?.content, this.name);
  }
}
