import { NextApiRequest, NextApiResponse } from 'next';
import { createAIProvider } from '../../lib/ai-providers';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { imageBase64 } = req.body;

    if (!imageBase64) {
      return res.status(400).json({ error: 'No image provided' });
    }

    // Create AI provider based on configuration
    const aiProvider = createAIProvider();

    // Analyze the photo using the selected provider
    const avatarConfig = await aiProvider.analyzePhoto(imageBase64);

    return res.status(200).json({
      avatarConfig,
      provider: aiProvider.name,
    });
  } catch (error) {
    return res.status(500).json({
      error: 'Failed to analyze photo',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
