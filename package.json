{"name": "notion-avatar", "description": "An online tool for making notion-style avatars.", "version": "1.0.0", "private": true, "author": "may<PERSON><PERSON><<EMAIL>> (@phillzou)", "license": "MIT", "keywords": ["nextjs", "starter", "typescript", "notion"], "scripts": {"dev": "next", "build": "next build", "start": "next start", "type-check": "tsc", "lint": "eslint --ignore-path .gitignore \"src/**/*.+(ts|js|tsx)\"", "format": "prettier --ignore-path .gitignore \"src/**/*.+(ts|js|tsx)\" --write", "postinstall": "husky install", "commit": "cz", "svgo": "svgo -f public/icon"}, "lint-staged": {"./src/**/*.{ts,js,jsx,tsx}": ["yarn lint --fix", "yarn format"]}, "dependencies": {"@google/generative-ai": "0.24.1", "chrome-aws-lambda": "8.0.2", "copy-to-clipboard": "3.3.1", "dayjs": "1.10.7", "html2canvas": "1.3.2", "next": "13.0.0", "next-i18next": "8.8.0", "next-pwa": "5.3.1", "openai": "5.11.0", "puppeteer-core": "10.4.0", "raw-loader": "4.0.2", "react": "18.2.0", "react-dom": "18.2.0", "react-hot-toast": "2.5.2"}, "devDependencies": {"@commitlint/cli": "13.2.1", "@commitlint/config-conventional": "13.2.0", "@types/dom-to-image": "2.6.4", "@types/file-saver": "2.0.5", "@types/node": "14.17.15", "@types/react": "17.0.30", "@types/react-dom": "17.0.9", "@typescript-eslint/eslint-plugin": "4.31.1", "@typescript-eslint/parser": "4.31.1", "autoprefixer": "10.4.4", "commitizen": "4.2.4", "cz-conventional-changelog": "3.3.0", "eslint": "7.32.0", "eslint-config-airbnb": "18.2.1", "eslint-config-next": "13.0.0", "eslint-config-prettier": "8.3.0", "eslint-import-resolver-typescript": "2.5.0", "eslint-plugin-import": "2.23.4", "eslint-plugin-jsx-a11y": "6.4.1", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-react": "7.24.0", "eslint-plugin-react-hooks": "4.2.0", "husky": "7.0.4", "lint-staged": "11.1.2", "postcss": "8.4.12", "prettier": "2.4.0", "puppeteer": "10.4.0", "svgo": "2.7.0", "tailwindcss": "3.0.23", "typescript": "4.4.3"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}}